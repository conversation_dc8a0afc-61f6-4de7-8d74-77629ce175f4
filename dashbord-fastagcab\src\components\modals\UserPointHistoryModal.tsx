import React, { useState, useEffect } from 'react'
import { X, TrendingUp, TrendingDown, Award, Calendar, Filter, Eye } from 'lucide-react'
import Card, { CardContent } from '../ui/Card'
import LoadingSpinner from '../ui/LoadingSpinner'
import toast from 'react-hot-toast'

interface PointHistoryItem {
  _id: string
  transactionType: 'earned' | 'redeemed' | 'adjusted' | 'expired' | 'bonus'
  pointsChange: number
  pointsBalance: number
  source: string
  description: string
  createdAt: string
  metadata?: {
    qrCode?: string
    productName?: string
    giftName?: string
    adminNote?: string
  }
}

interface UserPointHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  userName: string
}

export default function UserPointHistoryModal({ 
  isOpen, 
  onClose, 
  userId, 
  userName 
}: UserPointHistoryModalProps) {
  const [history, setHistory] = useState<PointHistoryItem[]>([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [selectedItem, setSelectedItem] = useState<PointHistoryItem | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)

  const fetchUserPointHistory = async (page = 1, filter = 'all') => {
    if (!userId) return

    try {
      setLoading(true)
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        sortOrder: 'desc'
      })

      if (filter !== 'all') {
        queryParams.append('transactionType', filter)
      }

      const response = await fetch(`api/points/history/all?userId=${userId}&${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        setHistory(data.data.history)
        setCurrentPage(data.data.pagination.currentPage)
        setTotalPages(data.data.pagination.totalPages)
        setTotalCount(data.data.pagination.totalCount)
      } else {
        toast.error(data.message || 'Failed to fetch point history')
      }
    } catch (error) {
      console.error('Fetch user point history error:', error)
      toast.error('Failed to fetch point history')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen && userId) {
      fetchUserPointHistory(1, selectedFilter)
    }
  }, [isOpen, userId, selectedFilter])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchUserPointHistory(page, selectedFilter)
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'earned': return { text: '#1ca63a', bg: '#1ca63a15' }
      case 'redeemed': return { text: '#df5921', bg: '#df592115' }
      case 'adjusted': return { text: '#7e8689', bg: '#7e868915' }
      case 'bonus': return { text: '#d5a81a', bg: '#d5a81a15' }
      case 'expired': return { text: '#7e8689', bg: '#7e868915' }
      default: return { text: '#7e8689', bg: '#7e868915' }
    }
  }

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'qr_scan': return '📱'
      case 'gift_redemption': return '🎁'
      case 'admin_adjustment': return '⚙️'
      case 'bonus_reward': return '⭐'
      case 'referral': return '👥'
      default: return '📋'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-0 border w-full max-w-4xl shadow-lg rounded-lg bg-white mb-4">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b" style={{ borderColor: '#7e8689' }}>
          <div>
            <h2 className="text-xl font-bold" style={{ color: '#1A1A1A' }}>
              Point History - {userName}
            </h2>
            <p style={{ color: '#7e8689' }}>View all point transactions for this user</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            style={{ color: '#7e8689' }}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Filters */}
        <div className="p-6 border-b" style={{ borderColor: '#7e8689' }}>
          <div className="flex items-center gap-4">
            <Filter className="h-5 w-5" style={{ color: '#7e8689' }} />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="border rounded-md px-3 py-2 focus:outline-none focus:ring-2"
              style={{ 
                borderColor: '#7e8689',
                color: '#1A1A1A',
                focusRingColor: '#1ca63a'
              }}
            >
              <option value="all">All Transactions</option>
              <option value="earned">Earned Points</option>
              <option value="redeemed">Redeemed Points</option>
              <option value="adjusted">Adjusted Points</option>
              <option value="bonus">Bonus Points</option>
              <option value="expired">Expired Points</option>
            </select>
            <span style={{ color: '#7e8689' }}>
              Total: {totalCount} transactions
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <LoadingSpinner />
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-8">
              <p style={{ color: '#7e8689' }}>No point history found for this user.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((item) => {
                const colors = getTransactionTypeColor(item.transactionType)
                return (
                  <Card key={item._id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div 
                            className="p-3 rounded-lg"
                            style={{ backgroundColor: colors.bg }}
                          >
                            {item.transactionType === 'earned' && <TrendingUp className="h-5 w-5" style={{ color: colors.text }} />}
                            {item.transactionType === 'redeemed' && <TrendingDown className="h-5 w-5" style={{ color: colors.text }} />}
                            {(item.transactionType === 'adjusted' || item.transactionType === 'bonus') && <Award className="h-5 w-5" style={{ color: colors.text }} />}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <span 
                                className="px-2 py-1 text-xs font-semibold rounded-full"
                                style={{ 
                                  color: colors.text,
                                  backgroundColor: colors.bg
                                }}
                              >
                                {item.transactionType}
                              </span>
                              <span style={{ color: '#7e8689' }}>
                                {getSourceIcon(item.source)} {item.source.replace('_', ' ')}
                              </span>
                            </div>
                            <p className="text-sm mt-1" style={{ color: '#1A1A1A' }}>
                              {item.description}
                            </p>
                            <p className="text-xs mt-1" style={{ color: '#7e8689' }}>
                              {formatDate(item.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p 
                            className="text-lg font-bold"
                            style={{ 
                              color: item.pointsChange > 0 ? '#1ca63a' : '#df5921'
                            }}
                          >
                            {item.pointsChange > 0 ? '+' : ''}{item.pointsChange}
                          </p>
                          <p className="text-sm" style={{ color: '#7e8689' }}>
                            Balance: {item.pointsBalance}
                          </p>
                          <button
                            onClick={() => {
                              setSelectedItem(item)
                              setShowDetailsModal(true)
                            }}
                            className="text-xs mt-1 flex items-center gap-1 hover:underline"
                            style={{ color: '#1ca63a' }}
                          >
                            <Eye className="h-3 w-3" />
                            Details
                          </button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t" style={{ borderColor: '#7e8689' }}>
              <div className="text-sm" style={{ color: '#7e8689' }}>
                Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} results
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-2 text-sm font-medium border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ 
                    color: '#1A1A1A',
                    borderColor: '#7e8689'
                  }}
                >
                  Previous
                </button>
                <span className="px-3 py-2 text-sm" style={{ color: '#7e8689' }}>
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 text-sm font-medium border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ 
                    color: '#1A1A1A',
                    borderColor: '#7e8689'
                  }}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t" style={{ borderColor: '#7e8689' }}>
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium rounded-md hover:opacity-90 transition-opacity"
            style={{ 
              backgroundColor: '#7e8689',
              color: '#ffffff'
            }}
          >
            Close
          </button>
        </div>
      </div>

      {/* Details Modal */}
      {showDetailsModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-60">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium" style={{ color: '#1A1A1A' }}>
                  Transaction Details
                </h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="hover:opacity-70"
                  style={{ color: '#7e8689' }}
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium" style={{ color: '#7e8689' }}>Transaction Type</label>
                  <span 
                    className="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                    style={{ 
                      color: getTransactionTypeColor(selectedItem.transactionType).text,
                      backgroundColor: getTransactionTypeColor(selectedItem.transactionType).bg
                    }}
                  >
                    {selectedItem.transactionType}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium" style={{ color: '#7e8689' }}>Points Change</label>
                  <p 
                    className="text-lg font-bold"
                    style={{ 
                      color: selectedItem.pointsChange > 0 ? '#1ca63a' : '#df5921'
                    }}
                  >
                    {selectedItem.pointsChange > 0 ? '+' : ''}{selectedItem.pointsChange}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium" style={{ color: '#7e8689' }}>Balance After</label>
                  <p className="text-sm" style={{ color: '#1A1A1A' }}>{selectedItem.pointsBalance}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium" style={{ color: '#7e8689' }}>Source</label>
                  <p className="text-sm" style={{ color: '#1A1A1A' }}>{selectedItem.source.replace('_', ' ')}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium" style={{ color: '#7e8689' }}>Description</label>
                  <p className="text-sm" style={{ color: '#1A1A1A' }}>{selectedItem.description}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium" style={{ color: '#7e8689' }}>Date & Time</label>
                  <p className="text-sm" style={{ color: '#1A1A1A' }}>{formatDate(selectedItem.createdAt)}</p>
                </div>

                {selectedItem.metadata && (
                  <div>
                    <label className="block text-sm font-medium" style={{ color: '#7e8689' }}>Additional Details</label>
                    <div className="text-sm space-y-1" style={{ color: '#1A1A1A' }}>
                      {selectedItem.metadata.productName && (
                        <p><strong>Product:</strong> {selectedItem.metadata.productName}</p>
                      )}
                      {selectedItem.metadata.qrCode && (
                        <p><strong>QR Code:</strong> {selectedItem.metadata.qrCode}</p>
                      )}
                      {selectedItem.metadata.giftName && (
                        <p><strong>Gift:</strong> {selectedItem.metadata.giftName}</p>
                      )}
                      {selectedItem.metadata.adminNote && (
                        <p><strong>Admin Note:</strong> {selectedItem.metadata.adminNote}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-4 py-2 text-sm font-medium rounded-md hover:opacity-90"
                  style={{ 
                    backgroundColor: '#7e8689',
                    color: '#ffffff'
                  }}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
